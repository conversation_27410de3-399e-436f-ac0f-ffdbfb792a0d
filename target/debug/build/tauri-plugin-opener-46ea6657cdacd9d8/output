cargo:rerun-if-changed=permissions
cargo:PERMISSION_FILES_PATH=/Users/<USER>/code/kraken/app/tauri-test/tauri-app/target/debug/build/tauri-plugin-opener-46ea6657cdacd9d8/out/tauri-plugin-opener-permission-files
cargo:rerun-if-env-changed=REMOVE_UNUSED_COMMANDS
cargo:GLOBAL_SCOPE_SCHEMA_PATH=/Users/<USER>/code/kraken/app/tauri-test/tauri-app/target/debug/build/tauri-plugin-opener-46ea6657cdacd9d8/out/global-scope.json
cargo:GLOBAL_API_SCRIPT_PATH=/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-plugin-opener-2.2.3/api-iife.js
cargo:rustc-check-cfg=cfg(mobile)
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
cargo:rustc-check-cfg=cfg(mobile)
