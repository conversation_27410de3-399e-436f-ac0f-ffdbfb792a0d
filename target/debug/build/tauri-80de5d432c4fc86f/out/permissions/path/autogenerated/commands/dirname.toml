# Copyright 2019-2024 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT
# Automatically generated - DO NOT EDIT!

[[permission]]
identifier = "allow-dirname"
description = "Enables the dirname command without any pre-configured scope."
commands.allow = ["dirname"]

[[permission]]
identifier = "deny-dirname"
description = "Denies the dirname command without any pre-configured scope."
commands.deny = ["dirname"]
