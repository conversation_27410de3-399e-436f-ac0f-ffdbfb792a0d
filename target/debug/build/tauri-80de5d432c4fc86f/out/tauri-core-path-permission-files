["/Users/<USER>/code/kraken/app/tauri-test/tauri-app/target/debug/build/tauri-80de5d432c4fc86f/out/permissions/path/autogenerated/commands/basename.toml", "/Users/<USER>/code/kraken/app/tauri-test/tauri-app/target/debug/build/tauri-80de5d432c4fc86f/out/permissions/path/autogenerated/commands/dirname.toml", "/Users/<USER>/code/kraken/app/tauri-test/tauri-app/target/debug/build/tauri-80de5d432c4fc86f/out/permissions/path/autogenerated/commands/extname.toml", "/Users/<USER>/code/kraken/app/tauri-test/tauri-app/target/debug/build/tauri-80de5d432c4fc86f/out/permissions/path/autogenerated/commands/is_absolute.toml", "/Users/<USER>/code/kraken/app/tauri-test/tauri-app/target/debug/build/tauri-80de5d432c4fc86f/out/permissions/path/autogenerated/commands/join.toml", "/Users/<USER>/code/kraken/app/tauri-test/tauri-app/target/debug/build/tauri-80de5d432c4fc86f/out/permissions/path/autogenerated/commands/normalize.toml", "/Users/<USER>/code/kraken/app/tauri-test/tauri-app/target/debug/build/tauri-80de5d432c4fc86f/out/permissions/path/autogenerated/commands/resolve.toml", "/Users/<USER>/code/kraken/app/tauri-test/tauri-app/target/debug/build/tauri-80de5d432c4fc86f/out/permissions/path/autogenerated/commands/resolve_directory.toml", "/Users/<USER>/code/kraken/app/tauri-test/tauri-app/target/debug/build/tauri-80de5d432c4fc86f/out/permissions/path/autogenerated/default.toml"]