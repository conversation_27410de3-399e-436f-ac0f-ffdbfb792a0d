{"rustc": 17575471286409424799, "features": "[\"build\"]", "declared_features": "[\"build\", \"runtime\"]", "target": 15996119522804316622, "profile": 3033921117576893, "path": 10894721688813197652, "deps": [[4352886507220678900, "serde_json", false, 3734553977443763343], [6913375703034175521, "schemars", false, 10836473175385576320], [9293239362693504808, "glob", false, 16132827489811880175], [9689903380558560274, "serde", false, 4612710251836842067], [11207653606310558077, "anyhow", false, 12045789377328738202], [12060164242600251039, "toml", false, 12233588531366240322], [14687315689416489882, "plist", false, 5436107153753749107], [15622660310229662834, "walkdir", false, 13703257195583921410], [17233053221795943287, "tauri_utils", false, 15567486500045153590]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-plugin-771546c757bff523/dep-lib-tauri_plugin", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}