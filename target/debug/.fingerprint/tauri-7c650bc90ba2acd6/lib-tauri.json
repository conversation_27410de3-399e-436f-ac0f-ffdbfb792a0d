{"rustc": 17575471286409424799, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"dynamic-acl\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dynamic-acl\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\", \"x11\"]", "target": 12223948975794516716, "profile": 5347358027863023418, "path": 17351008758048286400, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 11570408695513071437], [1260461579271933187, "serialize_to_javascript", false, 1010566912671373112], [1605511829699343563, "objc2", false, 8824458592928133024], [1967864351173319501, "muda", false, 18031515132707076629], [3331586631144870129, "getrandom", false, 8508153423611531468], [4143744114649553716, "raw_window_handle", false, 7426449368153472290], [4352886507220678900, "serde_json", false, 10474371984721824485], [4537297827336760846, "thiserror", false, 18353082697783552068], [5404511084185685755, "url", false, 14192406561414026567], [5986029879202738730, "log", false, 15804037246207496926], [6537120525306722933, "tauri_macros", false, 6046032064997122288], [6803352382179706244, "percent_encoding", false, 15861969099466921154], [8589231650440095114, "embed_plist", false, 12529692096102226498], [9010263965687315507, "http", false, 15449950808543030683], [9293239362693504808, "glob", false, 16132827489811880175], [9628989939628929789, "objc2_web_kit", false, 4749993358133285463], [9689903380558560274, "serde", false, 9300845324791820181], [9859211262912517217, "objc2_foundation", false, 16390670556275870915], [9952368442187680820, "tauri_runtime_wry", false, 9364279249482869363], [10229185211513642314, "mime", false, 1830002369668452518], [10575598148575346675, "objc2_app_kit", false, 14050236394747444896], [11207653606310558077, "anyhow", false, 12045789377328738202], [11989259058781683633, "dunce", false, 617362622568736731], [12565293087094287914, "window_vibrancy", false, 5805767406477294903], [12986574360607194341, "serde_repr", false, 8313332237424354095], [13077543566650298139, "heck", false, 15217904866363059000], [14687315689416489882, "plist", false, 14709054267471792869], [16727543399706004146, "cookie", false, 11398798069014443416], [16928111194414003569, "dirs", false, 1211994374355442010], [17045726903344650895, "build_script_build", false, 10892629671237203591], [17233053221795943287, "tauri_utils", false, 12204441697453743711], [17531218394775549125, "tokio", false, 502365839043090264], [18010483002580779355, "tauri_runtime", false, 16152493906753292020]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-7c650bc90ba2acd6/dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}