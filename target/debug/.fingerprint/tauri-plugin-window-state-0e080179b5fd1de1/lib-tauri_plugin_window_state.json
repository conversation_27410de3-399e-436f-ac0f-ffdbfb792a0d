{"rustc": 17575471286409424799, "features": "[]", "declared_features": "[]", "target": 17935818335149578806, "profile": 5347358027863023418, "path": 5705426746803813926, "deps": [[4352886507220678900, "serde_json", false, 10474371984721824485], [4537297827336760846, "thiserror", false, 18353082697783552068], [4707735785701411121, "build_script_build", false, 2799332173304606297], [5986029879202738730, "log", false, 15804037246207496926], [9689903380558560274, "serde", false, 9300845324791820181], [15840480199427237938, "bitflags", false, 17243200882417697526], [17045726903344650895, "tauri", false, 357098184463720411]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-plugin-window-state-0e080179b5fd1de1/dep-lib-tauri_plugin_window_state", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}