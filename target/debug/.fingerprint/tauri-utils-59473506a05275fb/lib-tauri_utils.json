{"rustc": 17575471286409424799, "features": "[\"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"html-manipulation\", \"proc-macro2\", \"quote\", \"resources\", \"schema\", \"schemars\", \"swift-rs\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 3033921117576893, "path": 1567910122242995040, "deps": [[373107762698212489, "proc_macro2", false, 4358740086776005285], [503635761244294217, "regex", false, 13043739189303172524], [1200537532907108615, "url<PERSON><PERSON>n", false, 14682695399232917238], [1678291836268844980, "brotli", false, 11644377362943500268], [2995469292676432503, "uuid", false, 17261084224269497715], [4071963112282141418, "serde_with", false, 10449044855444135997], [4352886507220678900, "serde_json", false, 3734553977443763343], [4537297827336760846, "thiserror", false, 18353082697783552068], [4899080583175475170, "semver", false, 12037507942404483939], [5404511084185685755, "url", false, 11745166191820358889], [5986029879202738730, "log", false, 15804037246207496926], [6606131838865521726, "ctor", false, 1734854722239726381], [6913375703034175521, "schemars", false, 10836473175385576320], [7170110829644101142, "json_patch", false, 4089154706953501970], [9010263965687315507, "http", false, 15449950808543030683], [9293239362693504808, "glob", false, 16132827489811880175], [9689903380558560274, "serde", false, 4612710251836842067], [11207653606310558077, "anyhow", false, 12045789377328738202], [11655476559277113544, "cargo_metadata", false, 3562737762057531075], [11989259058781683633, "dunce", false, 617362622568736731], [12060164242600251039, "toml", false, 12233588531366240322], [14232843520438415263, "html5ever", false, 3196556787863294865], [14885200901422974105, "swift_rs", false, 14065790265165890891], [15088007382495681292, "kuchiki", false, 8977255886534178824], [15622660310229662834, "walkdir", false, 13703257195583921410], [15932120279885307830, "memchr", false, 6570285269053244734], [17146114186171651583, "infer", false, 9604441603495639771], [17183029615630212089, "serde_untagged", false, 13363155747921319529], [17186037756130803222, "phf", false, 3809121761655864286], [17990358020177143287, "quote", false, 7558166186160421506]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-utils-59473506a05275fb/dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}