{"rustc": 17575471286409424799, "features": "[]", "declared_features": "[]", "target": 14253890914074675322, "profile": 6675295047989516842, "path": 8717037289094896424, "deps": [[2784153353110520258, "tauri_plugin_opener", false, 16609451444958635674], [4352886507220678900, "serde_json", false, 10474371984721824485], [4707735785701411121, "tauri_plugin_window_state", false, 16549655572087185394], [4776110772255712465, "tauri_plugin_single_instance", false, 77014822750821358], [7764858976065141659, "plugin_interface", false, 17028854208410372416], [9689903380558560274, "serde", false, 9300845324791820181], [13587469111750606423, "libloading", false, 4452233631963285216], [17045726903344650895, "tauri", false, 357098184463720411], [17968271835982830072, "tauri_app_lib", false, 1006474104147384983], [17968271835982830072, "build_script_build", false, 3572112960030965401]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-app-c26bf80dbb9496c1/dep-bin-tauri-app", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}