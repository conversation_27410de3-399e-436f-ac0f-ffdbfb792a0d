{"rustc": 17575471286409424799, "features": "[\"alloc\", \"std\"]", "declared_features": "[\"alloc\", \"apple\", \"catch-all\", \"default\", \"exception\", \"gnustep-1-7\", \"gnustep-1-8\", \"gnustep-1-9\", \"gnustep-2-0\", \"gnustep-2-1\", \"malloc\", \"malloc_buf\", \"objc2-proc-macros\", \"relax-sign-encoding\", \"relax-void-encoding\", \"std\", \"unstable-apple-new\", \"unstable-autoreleasesafe\", \"unstable-c-unwind\", \"unstable-compiler-rt\", \"unstable-msg-send-always-comma\", \"unstable-static-class\", \"unstable-static-class-inlined\", \"unstable-static-sel\", \"unstable-static-sel-inlined\", \"verify\"]", "target": 14277124324407714732, "profile": 6305132673592726323, "path": 11150585935334657834, "deps": [[4595351064294307802, "objc2_encode", false, 2421431750892912159], [6452682797676277333, "objc_sys", false, 6577290507974206669]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/objc2-ed980f47feca01db/dep-lib-objc2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}