{"rustc": 17575471286409424799, "features": "[\"drag-drop\", \"gdkx11\", \"javascriptcore-rs\", \"linux-body\", \"os-webview\", \"protocol\", \"soup3\", \"webkit2gtk\", \"webkit2gtk-sys\", \"x11\", \"x11-dl\"]", "declared_features": "[\"default\", \"devtools\", \"drag-drop\", \"fullscreen\", \"gdkx11\", \"javascriptcore-rs\", \"linux-body\", \"mac-proxy\", \"os-webview\", \"protocol\", \"serde\", \"soup3\", \"tracing\", \"transparent\", \"webkit2gtk\", \"webkit2gtk-sys\", \"x11\", \"x11-dl\"]", "target": 2463569863749872413, "profile": 3543196331859982759, "path": 15437413428264088585, "deps": [[309970253587158206, "block2", false, 2152834081932040461], [1605511829699343563, "objc2", false, 8824458592928133024], [3722963349756955755, "once_cell", false, 11797297199178840105], [4143744114649553716, "raw_window_handle", false, 7426449368153472290], [4537297827336760846, "thiserror", false, 18353082697783552068], [5404511084185685755, "url", false, 14192406561414026567], [7606335748176206944, "dpi", false, 16595955345629805642], [8558698349995473911, "build_script_build", false, 8933907247592668015], [9010263965687315507, "http", false, 15449950808543030683], [9628989939628929789, "objc2_web_kit", false, 4749993358133285463], [9859211262912517217, "objc2_foundation", false, 16390670556275870915], [10378802769730441691, "objc2_core_foundation", false, 15171655361789724077], [10575598148575346675, "objc2_app_kit", false, 14050236394747444896], [16727543399706004146, "cookie", false, 11398798069014443416], [16928111194414003569, "dirs", false, 1211994374355442010]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/wry-15bba6b5ad2438fb/dep-lib-wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}