{"rustc": 17575471286409424799, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 5347358027863023418, "path": 13016774742616669698, "deps": [[1605511829699343563, "objc2", false, 8824458592928133024], [4143744114649553716, "raw_window_handle", false, 7426449368153472290], [4352886507220678900, "serde_json", false, 10474371984721824485], [4537297827336760846, "thiserror", false, 18353082697783552068], [5404511084185685755, "url", false, 14192406561414026567], [7606335748176206944, "dpi", false, 16595955345629805642], [9010263965687315507, "http", false, 15449950808543030683], [9628989939628929789, "objc2_web_kit", false, 4749993358133285463], [9689903380558560274, "serde", false, 9300845324791820181], [16727543399706004146, "cookie", false, 11398798069014443416], [17233053221795943287, "tauri_utils", false, 12204441697453743711], [18010483002580779355, "build_script_build", false, 1565989473022029382]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-runtime-abba0edbcd2bde19/dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}