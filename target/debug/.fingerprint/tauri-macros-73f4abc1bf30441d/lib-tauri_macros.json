{"rustc": 17575471286409424799, "features": "[\"compression\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 3033921117576893, "path": 4157422669077336776, "deps": [[373107762698212489, "proc_macro2", false, 4358740086776005285], [3412097196613774653, "tauri_codegen", false, 5621008958367337504], [13077543566650298139, "heck", false, 15217904866363059000], [17233053221795943287, "tauri_utils", false, 15567486500045153590], [17332570067994900305, "syn", false, 3716726457778649828], [17990358020177143287, "quote", false, 7558166186160421506]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-macros-73f4abc1bf30441d/dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}