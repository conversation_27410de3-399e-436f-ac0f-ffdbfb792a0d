{"rustc": 17575471286409424799, "features": "[\"brotli\", \"compression\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 5347358027863023418, "path": 1567910122242995040, "deps": [[503635761244294217, "regex", false, 13043739189303172524], [1200537532907108615, "url<PERSON><PERSON>n", false, 11570408695513071437], [1678291836268844980, "brotli", false, 11644377362943500268], [2995469292676432503, "uuid", false, 1151545859745318467], [4071963112282141418, "serde_with", false, 705879927749483254], [4352886507220678900, "serde_json", false, 10474371984721824485], [4537297827336760846, "thiserror", false, 18353082697783552068], [4899080583175475170, "semver", false, 9562705521250160998], [5404511084185685755, "url", false, 14192406561414026567], [5986029879202738730, "log", false, 15804037246207496926], [6606131838865521726, "ctor", false, 1734854722239726381], [7170110829644101142, "json_patch", false, 9012630244115964005], [9010263965687315507, "http", false, 15449950808543030683], [9293239362693504808, "glob", false, 16132827489811880175], [9689903380558560274, "serde", false, 9300845324791820181], [11207653606310558077, "anyhow", false, 12045789377328738202], [11989259058781683633, "dunce", false, 617362622568736731], [12060164242600251039, "toml", false, 9404674135783012986], [15622660310229662834, "walkdir", false, 13703257195583921410], [15932120279885307830, "memchr", false, 6570285269053244734], [17146114186171651583, "infer", false, 16456313284561800132], [17183029615630212089, "serde_untagged", false, 14226840832898754599], [17186037756130803222, "phf", false, 6226547883244893453]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-utils-9d6257c23be32ecf/dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}