{"rustc": 17575471286409424799, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 3033921117576893, "path": 3924153452782421526, "deps": [[4352886507220678900, "serde_json", false, 3734553977443763343], [4824857623768494398, "cargo_toml", false, 12285270862406085130], [4899080583175475170, "semver", false, 12037507942404483939], [5165059047667588304, "tauri_winres", false, 12602356673810759660], [6913375703034175521, "schemars", false, 10836473175385576320], [7170110829644101142, "json_patch", false, 4089154706953501970], [9293239362693504808, "glob", false, 16132827489811880175], [9689903380558560274, "serde", false, 4612710251836842067], [11207653606310558077, "anyhow", false, 12045789377328738202], [12060164242600251039, "toml", false, 12233588531366240322], [13077543566650298139, "heck", false, 15217904866363059000], [15622660310229662834, "walkdir", false, 13703257195583921410], [16928111194414003569, "dirs", false, 1211994374355442010], [17233053221795943287, "tauri_utils", false, 15567486500045153590]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-build-4374c2eae7c0d72a/dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}