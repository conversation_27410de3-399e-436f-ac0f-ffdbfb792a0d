{"rustc": 17575471286409424799, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[17045726903344650895, "build_script_build", false, 10892629671237203591], [17968271835982830072, "build_script_build", false, 17830421145533969855], [2784153353110520258, "build_script_build", false, 1761103735025884885], [4707735785701411121, "build_script_build", false, 2799332173304606297]], "local": [{"RerunIfChanged": {"output": "debug/build/tauri-app-f42b46332a0ea6e6/output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}