{"rustc": 17575471286409424799, "features": "[\"NSAttributeDescription\", \"NSEntityDescription\", \"NSFetchRequest\", \"NSManagedObjectContext\", \"NSManagedObjectModel\", \"NSPersistentStoreRequest\", \"NSPropertyDescription\", \"bitflags\"]", "declared_features": "[\"CloudKit\", \"CoreDataDefines\", \"CoreDataErrors\", \"NSAtomicStore\", \"NSAtomicStoreCacheNode\", \"NSAttributeDescription\", \"NSBatchDeleteRequest\", \"NSBatchInsertRequest\", \"NSBatchUpdateRequest\", \"NSCompositeAttributeDescription\", \"NSCoreDataCoreSpotlightDelegate\", \"NSCustomMigrationStage\", \"NSDerivedAttributeDescription\", \"NSEntityDescription\", \"NSEntityMapping\", \"NSEntityMigrationPolicy\", \"NSExpressionDescription\", \"NSFetchIndexDescription\", \"NSFetchIndexElementDescription\", \"NSFetchRequest\", \"NSFetchRequestExpression\", \"NSFetchedPropertyDescription\", \"NSFetchedResultsController\", \"NSIncrementalStore\", \"NSIncrementalStoreNode\", \"NSLightweightMigrationStage\", \"NSManagedObject\", \"NSManagedObjectContext\", \"NSManagedObjectID\", \"NSManagedObjectModel\", \"NSManagedObjectModelReference\", \"NSMappingModel\", \"NSMergePolicy\", \"NSMigrationManager\", \"NSMigrationStage\", \"NSPersistentCloudKitContainer\", \"NSPersistentCloudKitContainerEvent\", \"NSPersistentCloudKitContainerEventRequest\", \"NSPersistentCloudKitContainerOptions\", \"NSPersistentContainer\", \"NSPersistentHistoryChange\", \"NSPersistentHistoryChangeRequest\", \"NSPersistentHistoryToken\", \"NSPersistentHistoryTransaction\", \"NSPersistentStore\", \"NSPersistentStoreCoordinator\", \"NSPersistentStoreDescription\", \"NSPersistentStoreRequest\", \"NSPersistentStoreResult\", \"NSPropertyDescription\", \"NSPropertyMapping\", \"NSQueryGenerationToken\", \"NSRelationshipDescription\", \"NSSaveChangesRequest\", \"NSStagedMigrationManager\", \"alloc\", \"bitflags\", \"block2\", \"default\", \"gnustep-1-7\", \"gnustep-1-8\", \"gnustep-1-9\", \"gnustep-2-0\", \"gnustep-2-1\", \"objc2-cloud-kit\", \"std\"]", "target": 7515372154814490868, "profile": 8196097686603091492, "path": 419070113529779804, "deps": [[1605511829699343563, "objc2", false, 8824458592928133024], [9859211262912517217, "objc2_foundation", false, 16390670556275870915], [15840480199427237938, "bitflags", false, 17243200882417697526]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/objc2-core-data-ca0fc753303286d4/dep-lib-objc2_core_data", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}