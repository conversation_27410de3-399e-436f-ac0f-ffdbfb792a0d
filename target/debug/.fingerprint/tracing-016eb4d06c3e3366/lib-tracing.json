{"rustc": 17575471286409424799, "features": "[\"attributes\", \"default\", \"std\", \"tracing-attributes\"]", "declared_features": "[\"async-await\", \"attributes\", \"default\", \"log\", \"log-always\", \"max_level_debug\", \"max_level_error\", \"max_level_info\", \"max_level_off\", \"max_level_trace\", \"max_level_warn\", \"release_max_level_debug\", \"release_max_level_error\", \"release_max_level_info\", \"release_max_level_off\", \"release_max_level_trace\", \"release_max_level_warn\", \"std\", \"tracing-attributes\", \"valuable\"]", "target": 5568135053145998517, "profile": 1006155289083248400, "path": 10173713842450223344, "deps": [[325572602735163265, "tracing_attributes", false, 9599629738720596288], [1906322745568073236, "pin_project_lite", false, 8566220521012806726], [3424551429995674438, "tracing_core", false, 8808139939897144912]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tracing-016eb4d06c3e3366/dep-lib-tracing", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}