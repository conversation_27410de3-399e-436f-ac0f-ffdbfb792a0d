{"rustc": 17575471286409424799, "features": "[\"common-controls-v6\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"default\", \"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\", \"x11\"]", "target": 1901661049345253480, "profile": 5347358027863023418, "path": 6361347927733934535, "deps": [[1605511829699343563, "objc2", false, 8824458592928133024], [2172092324659420098, "tao", false, 2323028194776599036], [4143744114649553716, "raw_window_handle", false, 7426449368153472290], [5404511084185685755, "url", false, 14192406561414026567], [5986029879202738730, "log", false, 15804037246207496926], [8558698349995473911, "wry", false, 14442506436192949603], [9010263965687315507, "http", false, 15449950808543030683], [9859211262912517217, "objc2_foundation", false, 16390670556275870915], [9952368442187680820, "build_script_build", false, 13233069134706802182], [10575598148575346675, "objc2_app_kit", false, 14050236394747444896], [17233053221795943287, "tauri_utils", false, 12204441697453743711], [18010483002580779355, "tauri_runtime", false, 16152493906753292020]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-runtime-wry-7ae49a8f69a74771/dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}