{"rustc": 17575471286409424799, "features": "[\"FoundationErrors\", \"FoundationLegacySwiftCompatibility\", \"NSAffineTransform\", \"NSAppleEventDescriptor\", \"NSAppleEventManager\", \"NSAppleScript\", \"NSArchiver\", \"NSArray\", \"NSAttributedString\", \"NSAutoreleasePool\", \"NSBackgroundActivityScheduler\", \"NSBundle\", \"NSByteCountFormatter\", \"NSByteOrder\", \"NSCache\", \"NSCalendar\", \"NSCalendarDate\", \"NSCharacterSet\", \"NSClassDescription\", \"NSCoder\", \"NSComparisonPredicate\", \"NSCompoundPredicate\", \"NSConnection\", \"NSData\", \"NSDate\", \"NSDateComponentsFormatter\", \"NSDateFormatter\", \"NSDateInterval\", \"NSDateIntervalFormatter\", \"NSDebug\", \"NSDecimal\", \"NSDecimalNumber\", \"NSDictionary\", \"NSDistantObject\", \"NSDistributedLock\", \"NSDistributedNotificationCenter\", \"NSEnergyFormatter\", \"NSEnumerator\", \"NSError\", \"NSException\", \"NSExpression\", \"NSExtensionContext\", \"NSExtensionItem\", \"NSExtensionRequestHandling\", \"NSFileCoordinator\", \"NSFileHandle\", \"NSFileManager\", \"NSFilePresenter\", \"NSFileVersion\", \"NSFileWrapper\", \"NSFormatter\", \"NSGarbageCollector\", \"NSGeometry\", \"NSHFSFileTypes\", \"NSHTTPCookie\", \"NSHTTPCookieStorage\", \"NSHashTable\", \"NSHost\", \"NSISO8601DateFormatter\", \"NSIndexPath\", \"NSIndexSet\", \"NSInflectionRule\", \"NSInvocation\", \"NSItemProvider\", \"NSJSONSerialization\", \"NSKeyValueCoding\", \"NSKeyValueObserving\", \"NSKeyValueSharedObservers\", \"NSKeyedArchiver\", \"NSLengthFormatter\", \"NSLinguisticTagger\", \"NSListFormatter\", \"NSLocale\", \"NSLocalizedNumberFormatRule\", \"NSLock\", \"NSMapTable\", \"NSMassFormatter\", \"NSMeasurement\", \"NSMeasurementFormatter\", \"NSMetadata\", \"NSMetadataAttributes\", \"NSMethodSignature\", \"NSMorphology\", \"NSNetServices\", \"NSNotification\", \"NSNotificationQueue\", \"NSNull\", \"NSNumberFormatter\", \"NSObjCRuntime\", \"NSObject\", \"NSObjectScripting\", \"NSOperation\", \"NSOrderedCollectionChange\", \"NSOrderedCollectionDifference\", \"NSOrderedSet\", \"NSOrthography\", \"NSPathUtilities\", \"NSPersonNameComponents\", \"NSPersonNameComponentsFormatter\", \"NSPointerArray\", \"NSPointerFunctions\", \"NSPort\", \"NSPortCoder\", \"NSPortMessage\", \"NSPortNameServer\", \"NSPredicate\", \"NSProcessInfo\", \"NSProgress\", \"NSPropertyList\", \"NSProtocolChecker\", \"NSProxy\", \"NSRange\", \"NSRegularExpression\", \"NSRelativeDateTimeFormatter\", \"NSRunLoop\", \"NSScanner\", \"NSScriptClassDescription\", \"NSScriptCoercionHandler\", \"NSScriptCommand\", \"NSScriptCommandDescription\", \"NSScriptExecutionContext\", \"NSScriptKeyValueCoding\", \"NSScriptObjectSpecifiers\", \"NSScriptStandardSuiteCommands\", \"NSScriptSuiteRegistry\", \"NSScriptWhoseTests\", \"NSSet\", \"NSSortDescriptor\", \"NSSpellServer\", \"NSStream\", \"NSString\", \"NSTask\", \"NSTermOfAddress\", \"NSTextCheckingResult\", \"NSThread\", \"NSTimeZone\", \"NSTimer\", \"NSURL\", \"NSURLAuthenticationChallenge\", \"NSURLCache\", \"NSURLConnection\", \"NSURLCredential\", \"NSURLCredentialStorage\", \"NSURLDownload\", \"NSURLError\", \"NSURLHandle\", \"NSURLProtectionSpace\", \"NSURLProtocol\", \"NSURLRequest\", \"NSURLResponse\", \"NSURLSession\", \"NSUUID\", \"NSUbiquitousKeyValueStore\", \"NSUndoManager\", \"NSUnit\", \"NSUserActivity\", \"NSUserDefaults\", \"NSUserNotification\", \"NSUserScriptTask\", \"NSValue\", \"NSValueTransformer\", \"NSXMLDTD\", \"NSXMLDTDNode\", \"NSXMLDocument\", \"NSXMLElement\", \"NSXMLNode\", \"NSXMLNodeOptions\", \"NSXMLParser\", \"NSXPCConnection\", \"NSZone\", \"alloc\", \"bitflags\", \"block2\", \"default\", \"libc\", \"objc2-core-foundation\", \"std\"]", "declared_features": "[\"FoundationErrors\", \"FoundationLegacySwiftCompatibility\", \"NSAffineTransform\", \"NSAppleEventDescriptor\", \"NSAppleEventManager\", \"NSAppleScript\", \"NSArchiver\", \"NSArray\", \"NSAttributedString\", \"NSAutoreleasePool\", \"NSBackgroundActivityScheduler\", \"NSBundle\", \"NSByteCountFormatter\", \"NSByteOrder\", \"NSCache\", \"NSCalendar\", \"NSCalendarDate\", \"NSCharacterSet\", \"NSClassDescription\", \"NSCoder\", \"NSComparisonPredicate\", \"NSCompoundPredicate\", \"NSConnection\", \"NSData\", \"NSDate\", \"NSDateComponentsFormatter\", \"NSDateFormatter\", \"NSDateInterval\", \"NSDateIntervalFormatter\", \"NSDebug\", \"NSDecimal\", \"NSDecimalNumber\", \"NSDictionary\", \"NSDistantObject\", \"NSDistributedLock\", \"NSDistributedNotificationCenter\", \"NSEnergyFormatter\", \"NSEnumerator\", \"NSError\", \"NSException\", \"NSExpression\", \"NSExtensionContext\", \"NSExtensionItem\", \"NSExtensionRequestHandling\", \"NSFileCoordinator\", \"NSFileHandle\", \"NSFileManager\", \"NSFilePresenter\", \"NSFileVersion\", \"NSFileWrapper\", \"NSFormatter\", \"NSGarbageCollector\", \"NSGeometry\", \"NSHFSFileTypes\", \"NSHTTPCookie\", \"NSHTTPCookieStorage\", \"NSHashTable\", \"NSHost\", \"NSISO8601DateFormatter\", \"NSIndexPath\", \"NSIndexSet\", \"NSInflectionRule\", \"NSInvocation\", \"NSItemProvider\", \"NSJSONSerialization\", \"NSKeyValueCoding\", \"NSKeyValueObserving\", \"NSKeyValueSharedObservers\", \"NSKeyedArchiver\", \"NSLengthFormatter\", \"NSLinguisticTagger\", \"NSListFormatter\", \"NSLocale\", \"NSLocalizedNumberFormatRule\", \"NSLock\", \"NSMapTable\", \"NSMassFormatter\", \"NSMeasurement\", \"NSMeasurementFormatter\", \"NSMetadata\", \"NSMetadataAttributes\", \"NSMethodSignature\", \"NSMorphology\", \"NSNetServices\", \"NSNotification\", \"NSNotificationQueue\", \"NSNull\", \"NSNumberFormatter\", \"NSObjCRuntime\", \"NSObject\", \"NSObjectScripting\", \"NSOperation\", \"NSOrderedCollectionChange\", \"NSOrderedCollectionDifference\", \"NSOrderedSet\", \"NSOrthography\", \"NSPathUtilities\", \"NSPersonNameComponents\", \"NSPersonNameComponentsFormatter\", \"NSPointerArray\", \"NSPointerFunctions\", \"NSPort\", \"NSPortCoder\", \"NSPortMessage\", \"NSPortNameServer\", \"NSPredicate\", \"NSProcessInfo\", \"NSProgress\", \"NSPropertyList\", \"NSProtocolChecker\", \"NSProxy\", \"NSRange\", \"NSRegularExpression\", \"NSRelativeDateTimeFormatter\", \"NSRunLoop\", \"NSScanner\", \"NSScriptClassDescription\", \"NSScriptCoercionHandler\", \"NSScriptCommand\", \"NSScriptCommandDescription\", \"NSScriptExecutionContext\", \"NSScriptKeyValueCoding\", \"NSScriptObjectSpecifiers\", \"NSScriptStandardSuiteCommands\", \"NSScriptSuiteRegistry\", \"NSScriptWhoseTests\", \"NSSet\", \"NSSortDescriptor\", \"NSSpellServer\", \"NSStream\", \"NSString\", \"NSTask\", \"NSTermOfAddress\", \"NSTextCheckingResult\", \"NSThread\", \"NSTimeZone\", \"NSTimer\", \"NSURL\", \"NSURLAuthenticationChallenge\", \"NSURLCache\", \"NSURLConnection\", \"NSURLCredential\", \"NSURLCredentialStorage\", \"NSURLDownload\", \"NSURLError\", \"NSURLHandle\", \"NSURLProtectionSpace\", \"NSURLProtocol\", \"NSURLRequest\", \"NSURLResponse\", \"NSURLSession\", \"NSUUID\", \"NSUbiquitousKeyValueStore\", \"NSUndoManager\", \"NSUnit\", \"NSUserActivity\", \"NSUserDefaults\", \"NSUserNotification\", \"NSUserScriptTask\", \"NSValue\", \"NSValueTransformer\", \"NSXMLDTD\", \"NSXMLDTDNode\", \"NSXMLDocument\", \"NSXMLElement\", \"NSXMLNode\", \"NSXMLNodeOptions\", \"NSXMLParser\", \"NSXPCConnection\", \"NSZone\", \"alloc\", \"bitflags\", \"block2\", \"default\", \"gnustep-1-7\", \"gnustep-1-8\", \"gnustep-1-9\", \"gnustep-2-0\", \"gnustep-2-1\", \"libc\", \"objc2-core-foundation\", \"objc2-core-services\", \"std\", \"unstable-mutation-return-null\", \"unstable-static-nsstring\"]", "target": 12262679844544453048, "profile": 8196097686603091492, "path": 11070022245480416003, "deps": [[309970253587158206, "block2", false, 2152834081932040461], [1605511829699343563, "objc2", false, 8824458592928133024], [10378802769730441691, "objc2_core_foundation", false, 15171655361789724077], [11887305395906501191, "libc", false, 7025692410298521570], [15840480199427237938, "bitflags", false, 17243200882417697526]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/objc2-foundation-50d8c492f72f6826/dep-lib-objc2_foundation", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}