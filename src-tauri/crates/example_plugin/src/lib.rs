use plugin_interface::{Plugin, CoreAPI, PluginHandle};

struct ExamplePlugin;

impl Plugin for ExamplePlugin {
    fn name(&self) -> &str {
        "example-plugin"
    }

    fn on_load(&self, core: &dyn CoreAPI) {
        println!("Core data: {}", core.get_data());
    }

    fn on_unload(&self) {
        println!("Example plugin unloaded");
    }
}

#[no_mangle]
pub extern "C" fn _create_plugin() -> PluginHandle {
    Box::into_raw(Box::new(ExamplePlugin)) as PluginHandle
}