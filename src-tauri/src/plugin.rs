use libloading::{Library, Symbol};
use std::collections::HashMap;
use std::path::{Path, PathBuf};
use std::sync::{Arc, Mutex};
use plugin_interface::{Plugin, CoreAPI};

// // Plugin trait that all plugins must implement
// pub trait Plugin: Send + Sync {
//     fn name(&self) -> &str;
//     fn on_load(&self, core: CoreAPI);
//     fn on_unload(&self);
// }

// Core API struct to expose main app functionality
#[derive(Clone)]
pub struct CoreContext {
    app_state: Arc<Mutex<AppState>>,
    // Add other shared resources here
}

impl CoreContext {
    pub fn new(app_state: Arc<Mutex<AppState>>) -> Self {
        Self { app_state }
    }
}

impl CoreAPI for CoreContext {
    // Example core method
    fn get_data(&self) -> String {
        let state = self.app_state.lock().unwrap();
        state.data.clone()
    }
}

// App state example
pub struct AppState {
    pub data: String,
    // Add other application state fields
}

// Plugin manager to handle plugins
pub struct PluginManager {
    plugins: HashMap<String, LoadedPlugin>,
    core_api: CoreContext,
}

struct LoadedPlugin {
    library: Library,
    instance: Box<dyn Plugin>,
}

impl PluginManager {
    pub fn new(app_state: Arc<Mutex<AppState>>) -> Self {
        Self {
            plugins: HashMap::new(),
            core_api: CoreContext::new(app_state),
        }
    }

    pub fn load_plugin(&mut self, path: PathBuf) -> Result<(), Box<dyn std::error::Error>> {
        unsafe {
            let lib = Library::new(&path)?;
            let constructor: Symbol<fn() -> *mut dyn Plugin> = lib.get(b"_create_plugin")?;
            let boxed_raw = constructor();
            let instance = Box::from_raw(boxed_raw);

            let plugin_name = instance.name().to_string();
            instance.on_load(&self.core_api);

            self.plugins.insert(
                plugin_name,
                LoadedPlugin {
                    library: lib,
                    instance,
                },
            );

            Ok(())
        }
    }

    pub fn unload_plugin(&mut self, name: &str) {
        if let Some(plugin) = self.plugins.remove(name) {
            plugin.instance.on_unload();
            // Library is dropped here and unloaded
        }
    }

    pub fn get_plugin(&self, name: &str) -> Option<&dyn Plugin> {
        self.plugins.get(name).map(|p| p.instance.as_ref())
    }

    pub fn scan_and_load(&mut self, plugin_dir: &Path) {
        if let Ok(entries) = std::fs::read_dir(plugin_dir) {
            for entry in entries.filter_map(Result::ok) {
                let path = entry.path();
                if is_valid_plugin_file(&path) {
                    if let Err(e) = self.load_plugin(path) {
                        eprintln!("Failed to load plugin: {}", e);
                    }
                }
            }
        }
    }
}

fn is_valid_plugin_file(path: &Path) -> bool {
    let extension = path.extension().and_then(|s| s.to_str());
    #[cfg(target_os = "windows")]
    return extension == Some("dll");
    #[cfg(target_os = "macos")]
    return extension == Some("dylib");
    #[cfg(target_os = "linux")]
    return extension == Some("so");
}
